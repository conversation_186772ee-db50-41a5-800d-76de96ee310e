import logging
import argparse

from pathlib import Path

from battery_timeline.logging_config import setup_logging
from battery_timeline.analyzer import BatteryTimelineAnalyzer
from battery_timeline.orchestrator import BatteryTimelineGenerator

logger = logging.getLogger(__name__)


def run_generator() -> None:
    csv_file, stats_file = BatteryTimelineGenerator().run()
    print(f"✅  generator done → {csv_file}\n📊  stats → {stats_file}")


def run_analyzer(timeline: Path) -> None:
    analyzer = BatteryTimelineAnalyzer(timeline_path=timeline)
    age_csv, detailed_csv, stats = analyzer.run()
    print(f"✅  analyzer done:")
    if age_csv:
        print(f"   📄  battery age report → {age_csv}")
    if detailed_csv:
        print(f"   📄  detailed lifecycle report → {detailed_csv}")
    if stats:
        print(f"   📊  stats → {stats}")


if __name__ == "__main__":
    parser = argparse.ArgumentParser("battery-timeline")
    parser.add_argument(
        "--analyze",
        action="store_true",
        help="Run BatteryTimelineAnalyzer instead of the generator",
    )
    parser.add_argument(
        "-d", "--debug", action="store_true", help="Enable DEBUG level logging"
    )

    args = parser.parse_args()

    if args.analyze:
        setup_logging(
            debug=args.debug, log_file=Path("output/analyze_battery_timeline.log")
        )
        run_analyzer(Path("output/battery_lifecycle_timelines.csv"))
    else:
        setup_logging(debug=args.debug, log_file=Path("output/battery_timeline.log"))
        run_generator()
